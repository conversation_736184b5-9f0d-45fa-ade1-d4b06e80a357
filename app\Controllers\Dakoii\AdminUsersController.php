<?php

namespace App\Controllers\Dakoii;

use App\Controllers\Dakoii\DakoiiBaseController;
use App\Models\AdminUserModel;

class AdminUsersController extends DakoiiBaseController
{
    protected $adminUserModel;

    public function __construct()
    {
        $this->adminUserModel = new AdminUserModel();
    }

    /**
     * Display list of admin users (GET)
     */
    public function index()
    {
        $this->setPageTitle('Admin Users');
        $this->setSidebarActive('admin_users');
        $this->setBreadcrumbs([
            ['title' => 'Admin Users', 'url' => '/dakoii/admin-users', 'active' => true]
        ]);

        $search = $this->request->getGet('search') ?? '';
        $perPage = 10;

        $data = [
            'adminUsers' => $this->adminUserModel->getAdminUsers($perPage, $search),
            'pager' => $this->adminUserModel->pager,
            'search' => $search,
        ];

        return $this->render('dakoii/admin_users/admin_users_index', $data);
    }

    /**
     * Display create form (GET)
     */
    public function create()
    {
        $this->setPageTitle('Create Admin User');
        $this->setSidebarActive('admin_users_create');
        $this->setBreadcrumbs([
            ['title' => 'Admin Users', 'url' => '/dakoii/admin-users', 'active' => false],
            ['title' => 'Create New', 'url' => '/dakoii/admin-users/create', 'active' => true]
        ]);

        $data = [
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
            'statusOptions' => $this->adminUserModel->getStatusOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('dakoii/admin_users/admin_users_create', $data);
    }

    /**
     * Process create form (POST)
     */
    public function store()
    {
        // Validation rules
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[admin_users.username]',
            'email' => 'required|valid_email|max_length[100]|is_unique[admin_users.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]',
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'phone_number' => 'permit_empty|max_length[20]',
            'role' => 'required|in_list[supervisor,user,admin]',
            'is_active' => 'permit_empty|in_list[0,1]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Prepare data for insertion
        $data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'password_hash' => $this->request->getPost('password'), // Will be hashed by model
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone_number' => $this->request->getPost('phone_number'),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'created_by' => $this->getCurrentUserId()
        ];

        if ($this->adminUserModel->insert($data)) {
            $this->setFlashMessage('success', 'Admin user created successfully.');
            return redirect()->to('/dakoii/admin-users');
        }

        $this->setFlashMessage('error', 'Failed to create admin user. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Display single admin user (GET)
     */
    public function show($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);
        
        if (!$adminUser) {
            $this->setFlashMessage('error', 'Admin user not found.');
            return redirect()->to('/dakoii/admin-users');
        }

        $this->setPageTitle('View Admin User');
        $this->setSidebarActive('admin_users');
        $this->setBreadcrumbs([
            ['title' => 'Admin Users', 'url' => '/dakoii/admin-users', 'active' => false],
            ['title' => $adminUser['first_name'] . ' ' . $adminUser['last_name'], 'url' => '/dakoii/admin-users/' . $id, 'active' => true]
        ]);

        $data = [
            'adminUser' => $adminUser,
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
        ];

        return $this->render('dakoii/admin_users/admin_users_show', $data);
    }

    /**
     * Display edit form (GET)
     */
    public function edit($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);
        
        if (!$adminUser) {
            $this->setFlashMessage('error', 'Admin user not found.');
            return redirect()->to('/dakoii/admin-users');
        }

        $this->setPageTitle('Edit Admin User');
        $this->setSidebarActive('admin_users_edit');
        $this->setBreadcrumbs([
            ['title' => 'Admin Users', 'url' => '/dakoii/admin-users', 'active' => false],
            ['title' => $adminUser['first_name'] . ' ' . $adminUser['last_name'], 'url' => '/dakoii/admin-users/' . $id, 'active' => false],
            ['title' => 'Edit', 'url' => '/dakoii/admin-users/' . $id . '/edit', 'active' => true]
        ]);

        $data = [
            'adminUser' => $adminUser,
            'roleOptions' => $this->adminUserModel->getRoleOptions(),
            'statusOptions' => $this->adminUserModel->getStatusOptions(),
            'validation' => \Config\Services::validation(),
        ];

        return $this->render('dakoii/admin_users/admin_users_edit', $data);
    }

    /**
     * Process edit form (POST)
     */
    public function update($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);
        
        if (!$adminUser) {
            $this->setFlashMessage('error', 'Admin user not found.');
            return redirect()->to('/dakoii/admin-users');
        }

        // Validation rules
        $rules = [
            'username' => [
                'rules' => "required|min_length[3]|max_length[50]|is_unique[admin_users.username,id,{$id}]",
                'errors' => [
                    'required' => 'Username is required',
                    'min_length' => 'Username must be at least 3 characters long',
                    'max_length' => 'Username cannot exceed 50 characters',
                    'is_unique' => 'Username already exists'
                ]
            ],
            'email' => [
                'rules' => "required|valid_email|max_length[100]|is_unique[admin_users.email,id,{$id}]",
                'errors' => [
                    'required' => 'Email is required',
                    'valid_email' => 'Please enter a valid email address',
                    'max_length' => 'Email cannot exceed 100 characters',
                    'is_unique' => 'Email already exists'
                ]
            ],
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'phone_number' => 'permit_empty|max_length[20]',
            'role' => 'required|in_list[supervisor,user,admin]',
            'is_active' => 'permit_empty|in_list[0,1]'
        ];

        // Add password validation only if password is provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[4]';
            $rules['confirm_password'] = 'matches[password]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('validation', $this->validator);
        }

        // Prepare data for update
        $data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone_number' => $this->request->getPost('phone_number'),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ?? 1,
        ];

        // Add password if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password_hash'] = $this->request->getPost('password'); // Will be hashed by model
        }

        if ($this->adminUserModel->update($id, $data)) {
            $this->setFlashMessage('success', 'Admin user updated successfully.');
            return redirect()->to('/dakoii/admin-users/' . $id);
        }

        $this->setFlashMessage('error', 'Failed to update admin user. Please try again.');
        return redirect()->back()->withInput();
    }

    /**
     * Delete admin user (POST)
     */
    public function delete($id)
    {
        $adminUser = $this->adminUserModel->getAdminUser($id);
        
        if (!$adminUser) {
            $this->setFlashMessage('error', 'Admin user not found.');
            return redirect()->to('/dakoii/admin-users');
        }

        if ($this->adminUserModel->delete($id)) {
            $this->setFlashMessage('success', 'Admin user deleted successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to delete admin user. Please try again.');
        }

        return redirect()->to('/dakoii/admin-users');
    }
}
