<?php

namespace App\Controllers\Dakoii;

use App\Controllers\Dakoii\DakoiiBaseController;
use App\Models\DakoiiUserModel;
use App\Models\AdminUserModel;

class DashboardController extends DakoiiBaseController
{
    protected $userModel;
    protected $adminUserModel;

    public function __construct()
    {
        $this->userModel = new DakoiiUserModel();
        $this->adminUserModel = new AdminUserModel();
    }

    /**
     * Display dashboard (GET)
     */
    public function index()
    {
        $this->setPageTitle('Dashboard');
        $this->setSidebarActive('dashboard');
        $this->setBreadcrumbs([
            ['title' => 'Dashboard', 'url' => '/dakoii/dashboard', 'active' => true]
        ]);

        // Get dashboard statistics
        $data = [
            'stats' => $this->getDashboardStats(),
            'recentActivity' => $this->getRecentActivity(),
            'systemAlerts' => $this->getSystemAlerts(),
        ];

        return $this->render('dakoii/dashboard/index', $data);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        return [
            'total_dakoii_users' => $this->userModel->countAll(),
            'active_dakoii_users' => $this->userModel->getActiveUsersCount(),
            'total_agencies' => 0, // Will be implemented when agencies table is created
            'active_agencies' => 0, // Will be implemented when agencies table is created
            'total_admin_users' => $this->adminUserModel->countAll(),
            'active_admin_users' => $this->adminUserModel->getActiveUsersCount(),
        ];
    }

    /**
     * Get recent activity (placeholder for now)
     */
    private function getRecentActivity(): array
    {
        // This will be implemented when audit logging is added
        return [
            [
                'id' => 1,
                'action' => 'User Login',
                'description' => $this->currentUser['first_name'] . ' ' . $this->currentUser['last_name'] . ' logged in',
                'timestamp' => date('Y-m-d H:i:s'),
                'user' => $this->currentUser['first_name'] . ' ' . $this->currentUser['last_name'],
                'type' => 'info'
            ],
            [
                'id' => 2,
                'action' => 'System Access',
                'description' => 'Dashboard accessed',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                'user' => $this->currentUser['first_name'] . ' ' . $this->currentUser['last_name'],
                'type' => 'success'
            ]
        ];
    }

    /**
     * Get system alerts (placeholder for now)
     */
    private function getSystemAlerts(): array
    {
        // This will be implemented when system monitoring is added
        return [
            [
                'id' => 1,
                'title' => 'System Status',
                'message' => 'All systems operational',
                'type' => 'success',
                'timestamp' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'title' => 'Welcome',
                'message' => 'Welcome to the Dakoii Portal! You can manage admin users and agencies from here.',
                'type' => 'info',
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ];
    }
}
