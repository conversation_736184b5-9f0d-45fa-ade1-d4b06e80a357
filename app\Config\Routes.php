<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

/*
 * --------------------------------------------------------------------
 * Dakoii Portal Routes
 * --------------------------------------------------------------------
 */

// Dakoii Authentication Routes (No Auth Required)
$routes->group('dakoii', function($routes) {
    // Redirect root to login
    $routes->get('/', 'Dakoii\AuthController::login');

    // Authentication routes
    $routes->get('login', 'Da<PERSON><PERSON>\AuthController::login');
    $routes->post('authenticate', 'Da<PERSON>ii\AuthController::authenticate');
    $routes->post('logout', '<PERSON><PERSON><PERSON>\AuthController::logout');

    // Password reset routes
    $routes->get('forgot-password', 'Dakoii\AuthController::forgotPassword');
    $routes->post('send-reset-link', 'Dakoii\AuthController::sendResetLink');
});

// Dakoii Protected Routes (Auth Required)
$routes->group('dakoii', ['filter' => 'dakoii_auth'], function($routes) {
    // Dashboard
    $routes->get('dashboard', 'Dakoii\DashboardController::index');

    // Admin Users Management (Future implementation)
    $routes->group('admin-users', function($routes) {
        $routes->get('/', 'Dakoii\AdminUsersController::index');
        $routes->get('create', 'Dakoii\AdminUsersController::create');
        $routes->post('store', 'Dakoii\AdminUsersController::store');
        $routes->get('(:num)', 'Dakoii\AdminUsersController::show/$1');
        $routes->get('(:num)/edit', 'Dakoii\AdminUsersController::edit/$1');
        $routes->post('(:num)/update', 'Dakoii\AdminUsersController::update/$1');
        $routes->post('(:num)/delete', 'Dakoii\AdminUsersController::delete/$1');
    });

    // Agencies Management (Future implementation)
    $routes->group('agencies', function($routes) {
        $routes->get('/', 'Dakoii\AgenciesController::index');
        $routes->get('create', 'Dakoii\AgenciesController::create');
        $routes->post('store', 'Dakoii\AgenciesController::store');
        $routes->get('(:num)', 'Dakoii\AgenciesController::show/$1');
        $routes->get('(:num)/edit', 'Dakoii\AgenciesController::edit/$1');
        $routes->post('(:num)/update', 'Dakoii\AgenciesController::update/$1');
        $routes->post('(:num)/delete', 'Dakoii\AgenciesController::delete/$1');
    });

    // System Administration (Future implementation)
    $routes->group('system', function($routes) {
        $routes->get('audit-logs', 'Dakoii\SystemController::auditLogs');
        $routes->get('settings', 'Dakoii\SystemController::settings');
        $routes->post('settings/update', 'Dakoii\SystemController::updateSettings');
    });

    // Reports (Future implementation)
    $routes->get('reports', 'Dakoii\ReportsController::index');

    // Profile Management (Future implementation)
    $routes->get('profile', 'Dakoii\ProfileController::index');
    $routes->post('profile/update', 'Dakoii\ProfileController::update');
});
