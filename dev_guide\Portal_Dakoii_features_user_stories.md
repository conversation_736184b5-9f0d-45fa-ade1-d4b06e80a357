# Dakoii Portal - System Features & User Stories

## System Overview
The Dakoii Portal serves as the super administrator interface for the CHS PNG employee management system, providing system-wide control and foundational setup capabilities.

---

## 🎯 Core System Features

### 1. **Authentication & Access Control**
- Dedicated login route `/dakoii`
- Secure authentication using `dakoii_users` table
- Session management with elevated privileges
- Multi-factor authentication support
- Password policy enforcement

### 2. **Dark Theme Interface**
- Modern dark UI template
- Eye-strain reduction for extended use
- Professional administrative appearance
- Responsive design for desktop and mobile
- Accessibility compliance

### 3. **Organization User Management**
- CRUD operations for admin users
- Role-based permission assignment
- User status management (active/inactive)
- Admin user authentication setup

### 4. **Agency Management**
- Complete agency lifecycle management
- Geographic and operational categorization
- Agency hierarchy and relationships
- Status tracking and reporting

### 5. **System Administration**
- Global system settings
- Audit trail and logging
- Data backup and recovery
- Performance monitoring

---

## 👤 User Stories

### **Authentication & Security**

#### Story #1: Super Admin Login
**As a** Dakoii super administrator  
**I want to** access the system through a dedicated login portal  
**So that** I can manage the entire CHS PNG system securely  

**Acceptance Criteria:**
- Access login form at `/dakoii` route
- Authenticate using credentials stored in `dakoii_users` table
- Display dark-themed login interface
- Redirect to dashboard upon successful authentication
- Show appropriate error messages for failed login attempts
- Support password reset functionality

#### Story #2: Secure Session Management
**As a** Dakoii super administrator  
**I want to** have secure session management with appropriate timeouts  
**So that** unauthorized access is prevented when I step away  

**Acceptance Criteria:**
- Automatic session timeout after 30 minutes of inactivity
- Session extension prompt before timeout
- Secure logout functionality
- Protection against session hijacking
- Clear session data on logout

---

### **Organization User Management**

#### Story #3: Create Admin Users
**As a** Dakoii super administrator  
**I want to** create new admin users for the organization  
**So that** I can delegate administrative responsibilities  

**Acceptance Criteria:**
- Create new admin user with required details (name, email, phone, role)
- Assign specific permissions and access levels
- Generate secure temporary passwords
- Send welcome email with login instructions
- Set user status (active/pending/inactive)
- Validate unique email addresses

#### Story #4: Manage Admin User Roles
**As a** Dakoii super administrator  
**I want to** assign and modify admin user roles and permissions  
**So that** each admin has appropriate access to system functions  

**Acceptance Criteria:**
- Define role types (HR Manager, Regional Supervisor, System Admin)
- Assign multiple roles to single user if needed
- Modify permissions for existing users
- View complete permission matrix
- Audit trail for permission changes

#### Story #5: Update Admin User Information
**As a** Dakoii super administrator  
**I want to** update existing admin user details  
**So that** user information remains current and accurate  

**Acceptance Criteria:**
- Edit user personal information
- Update contact details
- Modify reporting relationships
- Change user status (activate/deactivate)
- Update role assignments
- Log all changes with timestamp and reason

#### Story #6: Delete Admin Users
**As a** Dakoii super administrator  
**I want to** deactivate or remove admin users from the system  
**So that** former employees cannot access the system  

**Acceptance Criteria:**
- Soft delete option (deactivate) vs hard delete
- Transfer ownership of created records to another admin
- Confirmation dialog before deletion
- Audit log of deleted users
- Ability to reactivate soft-deleted users

---

### **Agency Management**

#### Story #7: Create New Agency
**As a** Dakoii super administrator  
**I want to** create new health facility agencies in the system  
**So that** each CHS facility can be managed independently  

**Acceptance Criteria:**
- Enter agency details (name, type, location, contact information)
- Assign unique agency codes/IDs
- Set agency category (Hospital, Clinic, Health Center, etc.)
- Define geographic location (Province, District)
- Set operational status (Active, Under Construction, Planned)
- Upload facility photos and documentation

#### Story #8: Configure Agency Hierarchy
**As a** Dakoii super administrator  
**I want to** establish relationships between agencies  
**So that** reporting structures and oversight are properly defined  

**Acceptance Criteria:**
- Define parent-child relationships between facilities
- Assign regional oversight responsibilities
- Set up referral networks between facilities
- Configure reporting hierarchies
- Map geographic coverage areas

#### Story #9: Update Agency Information
**As a** Dakoii super administrator  
**I want to** modify existing agency details  
**So that** facility information remains current and accurate  

**Acceptance Criteria:**
- Edit facility contact information
- Update operational status
- Modify facility categories and services offered
- Change geographic assignments
- Update facility capacity and staffing levels
- Log all changes with audit trail

#### Story #10: Manage Agency Status
**As a** Dakoii super administrator  
**I want to** activate, deactivate, or temporarily suspend agencies  
**So that** system access reflects actual operational status  

**Acceptance Criteria:**
- Change agency status (Active, Inactive, Suspended, Under Maintenance)
- Set effective dates for status changes
- Notify affected users of status changes
- Prevent access to suspended agencies
- Maintain historical status records

---

### **System Administration**

#### Story #11: View System Dashboard
**As a** Dakoii super administrator  
**I want to** see a comprehensive system overview dashboard  
**So that** I can monitor overall system health and usage  

**Acceptance Criteria:**
- Display total number of agencies, admins, and employees
- Show system performance metrics
- View recent activity logs
- Display alerts and notifications
- Show geographic distribution of agencies
- Present usage statistics and trends

#### Story #12: Generate System Reports
**As a** Dakoii super administrator  
**I want to** generate comprehensive reports across all agencies  
**So that** I can analyze system-wide performance and compliance  

**Acceptance Criteria:**
- Generate agency status reports
- Create user activity reports
- Export data in multiple formats (PDF, Excel, CSV)
- Schedule automated reports
- Filter reports by date range, region, or agency type
- Send reports via email

#### Story #13: Manage System Settings
**As a** Dakoii super administrator  
**I want to** configure global system settings  
**So that** the system operates according to organizational policies  

**Acceptance Criteria:**
- Configure password policies
- Set session timeout durations
- Manage email templates and notifications
- Define system-wide validation rules
- Set up backup schedules
- Configure integration settings

#### Story #14: View Audit Logs
**As a** Dakoii super administrator  
**I want to** access comprehensive audit trails  
**So that** I can track all system changes and maintain accountability  

**Acceptance Criteria:**
- View all user actions across the system
- Filter logs by user, date, action type
- Export audit logs for compliance
- Search logs by specific criteria
- View detailed change history
- Monitor failed login attempts

---

## 🗄️ Database Schema Requirements

### dakoii_users Table
```sql
- id (Primary Key)
- username (Unique)
- email (Unique)
- password_hash
- first_name
- last_name
- phone_number
- is_active (Boolean)
- last_login_at
- created_at
- updated_at
- created_by
- two_factor_enabled (Boolean)
```

---

## 🎨 UI/UX Requirements

### Dark Theme Specifications
- **Primary Background**: Dark navy/charcoal (#1a1a1a, #2d3748)
- **Secondary Background**: Lighter gray panels (#374151, #4a5568)
- **Text Colors**: Light gray/white (#f7fafc, #e2e8f0)
- **Accent Colors**: CHS brand colors adapted for dark theme
- **Interactive Elements**: High contrast for accessibility
- **Form Elements**: Dark styled inputs with proper focus indicators

### Navigation Structure
- **Sidebar Navigation**: Collapsible dark sidebar with icons
- **Top Bar**: User profile, notifications, logout
- **Breadcrumbs**: Clear navigation path
- **Search Functionality**: Global search across admins and agencies

---

## 🔐 Security Requirements

### Access Control
- Super admin privileges across all system functions
- Separate authentication table (`dakoii_users`)
- Role-based access control (RBAC)
- API rate limiting
- Input validation and sanitization

### Data Protection
- Encrypted password storage
- Secure session management
- HTTPS enforcement
- Cross-site request forgery (CSRF) protection
- SQL injection prevention

---

## 📱 Technical Requirements

### Performance
- Dashboard load time < 2 seconds
- Search results returned < 1 second
- Support for concurrent super admin sessions
- Efficient pagination for large datasets

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Progressive Web App (PWA) capabilities
- Offline capability for critical functions

### Integration Points
- API endpoints for other portals
- External system integrations (NASFUND, IRC)
- Email service integration
- File upload and storage system