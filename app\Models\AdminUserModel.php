<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminUserModel extends Model
{
    protected $table = 'admin_users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email',
        'password_hash',
        'first_name',
        'last_name',
        'phone_number',
        'role',
        'agency_id',
        'is_active',
        'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[admin_users.username,id,{id}]',
        'email' => 'required|valid_email|max_length[100]|is_unique[admin_users.email,id,{id}]',
        'password_hash' => 'required|min_length[8]',
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'required|min_length[2]|max_length[50]',
        'phone_number' => 'permit_empty|max_length[20]',
        'role' => 'required|in_list[supervisor,user,admin]',
        'agency_id' => 'permit_empty|integer',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 50 characters',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'max_length' => 'Email cannot exceed 100 characters',
            'is_unique' => 'Email already exists'
        ],
        'password_hash' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters long'
        ],
        'first_name' => [
            'required' => 'First name is required',
            'min_length' => 'First name must be at least 2 characters long',
            'max_length' => 'First name cannot exceed 50 characters'
        ],
        'last_name' => [
            'required' => 'Last name is required',
            'min_length' => 'Last name must be at least 2 characters long',
            'max_length' => 'Last name cannot exceed 50 characters'
        ],
        'role' => [
            'required' => 'Role is required',
            'in_list' => 'Role must be supervisor, user, or admin'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password_hash']) && !empty($data['data']['password_hash'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password_hash'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Get all admin users with pagination
     */
    public function getAdminUsers(int $perPage = 10, string $search = '')
    {
        if (!empty($search)) {
            $this->groupStart()
                 ->like('first_name', $search)
                 ->orLike('last_name', $search)
                 ->orLike('username', $search)
                 ->orLike('email', $search)
                 ->groupEnd();
        }

        return $this->orderBy('created_at', 'DESC')->paginate($perPage);
    }

    /**
     * Get admin user by ID
     */
    public function getAdminUser(int $id): ?array
    {
        return $this->find($id);
    }

    /**
     * Get active admin users count
     */
    public function getActiveUsersCount(): int
    {
        return $this->where('is_active', 1)->countAllResults();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role): array
    {
        return $this->where('role', $role)->where('is_active', 1)->findAll();
    }

    /**
     * Get role options for forms
     */
    public function getRoleOptions(): array
    {
        return [
            'user' => 'User',
            'supervisor' => 'Supervisor',
            'admin' => 'Admin'
        ];
    }

    /**
     * Get status options for forms
     */
    public function getStatusOptions(): array
    {
        return [
            '1' => 'Active',
            '0' => 'Inactive'
        ];
    }

    /**
     * Verify password
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Update password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password_hash' => $hashedPassword]);
    }
}
