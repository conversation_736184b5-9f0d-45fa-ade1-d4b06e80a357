<nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom">
    <div class="container-fluid">
        <!-- Mobile Menu Toggle -->
        <button class="btn btn-outline-light d-md-none me-2" type="button" onclick="toggleSidebar()">
            <i class="bi bi-list"></i>
        </button>
        
        <!-- Page Title -->
        <span class="navbar-brand mb-0 h1">
            <?= esc($pageTitle ?? 'Dakoii Portal') ?>
        </span>
        
        <!-- Right Side Navigation -->
        <div class="navbar-nav ms-auto d-flex flex-row">
            <!-- Notifications -->
            <div class="nav-item dropdown me-3">
                <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-bell fs-5"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                        2
                        <span class="visually-hidden">unread notifications</span>
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                    <li class="dropdown-header">
                        <strong>Notifications</strong>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-info-circle text-info"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <div class="fw-bold">System Status</div>
                                    <div class="small text-muted">All systems operational</div>
                                    <div class="small text-muted"><?= date('M j, Y g:i A') ?></div>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-person-plus text-success"></i>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <div class="fw-bold">Welcome</div>
                                    <div class="small text-muted">Welcome to Dakoii Portal</div>
                                    <div class="small text-muted"><?= date('M j, Y g:i A') ?></div>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-center" href="#">
                            <small>View all notifications</small>
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- User Profile Dropdown -->
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                        <i class="bi bi-person-fill text-white"></i>
                    </div>
                    <span class="d-none d-sm-inline">
                        <?= esc($currentUser['first_name'] ?? 'User') ?> <?= esc($currentUser['last_name'] ?? '') ?>
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li class="dropdown-header">
                        <strong><?= esc($currentUser['first_name'] ?? 'User') ?> <?= esc($currentUser['last_name'] ?? '') ?></strong>
                        <br>
                        <small class="text-muted"><?= esc($currentUser['email'] ?? '') ?></small>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('dakoii/profile') ?>">
                            <i class="bi bi-person me-2"></i>
                            My Profile
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('dakoii/settings') ?>">
                            <i class="bi bi-gear me-2"></i>
                            Settings
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form action="<?= base_url('dakoii/logout') ?>" method="post" class="d-inline">
                            <?= csrf_field() ?>
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>
