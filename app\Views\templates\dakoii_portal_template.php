<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($pageTitle ?? 'Dakoii Portal') ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Dark Theme CSS -->
    <link href="<?= base_url('assets/css/dakoii-dark-theme.css') ?>" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    <meta name="csrf-hash" content="<?= csrf_hash() ?>">
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <div class="sidebar d-flex flex-column p-3" id="sidebar">
            <!-- Logo and Brand -->
            <div class="text-center mb-4">
                <img src="<?= base_url('assets/images/dakoii-logo.png') ?>" alt="Dakoii Logo" class="login-logo mb-2">
                <h5 class="text-light mb-0">Dakoii Portal</h5>
                <small class="text-muted">Super Administrator</small>
            </div>
            
            <hr class="text-muted">
            
            <!-- Navigation Menu -->
            <nav class="nav nav-pills flex-column">
                <!-- Dashboard -->
                <a href="<?= base_url('dakoii/dashboard') ?>" 
                   class="nav-link <?= ($sidebarActive === 'dashboard') ? 'active' : '' ?>">
                    <i class="bi bi-speedometer2"></i>
                    Dashboard
                </a>
                
                <!-- Admin Users Management -->
                <div class="nav-item">
                    <a href="#" 
                       class="nav-link <?= (in_array($sidebarActive, ['admin_users', 'admin_users_create', 'admin_users_edit'])) ? 'active' : '' ?>"
                       data-bs-toggle="collapse" 
                       data-bs-target="#adminUsersMenu" 
                       aria-expanded="<?= (in_array($sidebarActive, ['admin_users', 'admin_users_create', 'admin_users_edit'])) ? 'true' : 'false' ?>">
                        <i class="bi bi-people"></i>
                        Admin Users
                        <i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?= (in_array($sidebarActive, ['admin_users', 'admin_users_create', 'admin_users_edit'])) ? 'show' : '' ?>" 
                         id="adminUsersMenu">
                        <div class="nav nav-pills flex-column ms-3">
                            <a href="<?= base_url('dakoii/admin-users') ?>" 
                               class="nav-link <?= ($sidebarActive === 'admin_users') ? 'active' : '' ?>">
                                <i class="bi bi-list"></i>
                                View All
                            </a>
                            <a href="<?= base_url('dakoii/admin-users/create') ?>" 
                               class="nav-link <?= ($sidebarActive === 'admin_users_create') ? 'active' : '' ?>">
                                <i class="bi bi-plus-circle"></i>
                                Create New
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Agency Management -->
                <div class="nav-item">
                    <a href="#" 
                       class="nav-link <?= (in_array($sidebarActive, ['agencies', 'agencies_create', 'agencies_edit'])) ? 'active' : '' ?>"
                       data-bs-toggle="collapse" 
                       data-bs-target="#agenciesMenu" 
                       aria-expanded="<?= (in_array($sidebarActive, ['agencies', 'agencies_create', 'agencies_edit'])) ? 'true' : 'false' ?>">
                        <i class="bi bi-building"></i>
                        Agencies
                        <i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?= (in_array($sidebarActive, ['agencies', 'agencies_create', 'agencies_edit'])) ? 'show' : '' ?>" 
                         id="agenciesMenu">
                        <div class="nav nav-pills flex-column ms-3">
                            <a href="<?= base_url('dakoii/agencies') ?>" 
                               class="nav-link <?= ($sidebarActive === 'agencies') ? 'active' : '' ?>">
                                <i class="bi bi-list"></i>
                                View All
                            </a>
                            <a href="<?= base_url('dakoii/agencies/create') ?>" 
                               class="nav-link <?= ($sidebarActive === 'agencies_create') ? 'active' : '' ?>">
                                <i class="bi bi-plus-circle"></i>
                                Create New
                            </a>
                        </div>
                    </div>
                </div>
                
                <hr class="text-muted">
                
                <!-- System Administration -->
                <div class="nav-item">
                    <a href="#" 
                       class="nav-link <?= (in_array($sidebarActive, ['system', 'audit_logs', 'settings'])) ? 'active' : '' ?>"
                       data-bs-toggle="collapse" 
                       data-bs-target="#systemMenu" 
                       aria-expanded="<?= (in_array($sidebarActive, ['system', 'audit_logs', 'settings'])) ? 'true' : 'false' ?>">
                        <i class="bi bi-gear"></i>
                        System
                        <i class="bi bi-chevron-down ms-auto"></i>
                    </a>
                    <div class="collapse <?= (in_array($sidebarActive, ['system', 'audit_logs', 'settings'])) ? 'show' : '' ?>" 
                         id="systemMenu">
                        <div class="nav nav-pills flex-column ms-3">
                            <a href="<?= base_url('dakoii/system/audit-logs') ?>" 
                               class="nav-link <?= ($sidebarActive === 'audit_logs') ? 'active' : '' ?>">
                                <i class="bi bi-journal-text"></i>
                                Audit Logs
                            </a>
                            <a href="<?= base_url('dakoii/system/settings') ?>" 
                               class="nav-link <?= ($sidebarActive === 'settings') ? 'active' : '' ?>">
                                <i class="bi bi-sliders"></i>
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Reports -->
                <a href="<?= base_url('dakoii/reports') ?>" 
                   class="nav-link <?= ($sidebarActive === 'reports') ? 'active' : '' ?>">
                    <i class="bi bi-graph-up"></i>
                    Reports
                </a>
            </nav>
            
            <!-- Footer -->
            <div class="mt-auto pt-3">
                <hr class="text-muted">
                <div class="text-center">
                    <small class="text-muted">
                        <i class="bi bi-shield-check"></i>
                        Secure Portal
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-grow-1 main-content" style="margin-left: 250px;">
            <!-- Top Navigation -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom">
                <div class="container-fluid">
                    <!-- Mobile Menu Toggle -->
                    <button class="btn btn-outline-light d-md-none me-2" type="button" onclick="toggleSidebar()">
                        <i class="bi bi-list"></i>
                    </button>
                    
                    <!-- Page Title -->
                    <span class="navbar-brand mb-0 h1">
                        <?= esc($pageTitle ?? 'Dakoii Portal') ?>
                    </span>
                    
                    <!-- Right Side Navigation -->
                    <div class="navbar-nav ms-auto d-flex flex-row">
                        <!-- Notifications -->
                        <div class="nav-item dropdown me-3">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-bell fs-5"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem;">
                                    2
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <li class="dropdown-header">
                                    <strong>Notifications</strong>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi bi-info-circle text-info"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-2">
                                                <div class="fw-bold">System Status</div>
                                                <div class="small text-muted">All systems operational</div>
                                                <div class="small text-muted"><?= date('M j, Y g:i A') ?></div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi bi-person-plus text-success"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-2">
                                                <div class="fw-bold">Welcome</div>
                                                <div class="small text-muted">Welcome to Dakoii Portal</div>
                                                <div class="small text-muted"><?= date('M j, Y g:i A') ?></div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="#">
                                        <small>View all notifications</small>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- User Profile Dropdown -->
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="bi bi-person-fill text-white"></i>
                                </div>
                                <span class="d-none d-sm-inline">
                                    <?= esc($currentUser['first_name'] ?? 'User') ?> <?= esc($currentUser['last_name'] ?? '') ?>
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">
                                    <strong><?= esc($currentUser['first_name'] ?? 'User') ?> <?= esc($currentUser['last_name'] ?? '') ?></strong>
                                    <br>
                                    <small class="text-muted"><?= esc($currentUser['email'] ?? '') ?></small>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('dakoii/profile') ?>">
                                        <i class="bi bi-person me-2"></i>
                                        My Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('dakoii/settings') ?>">
                                        <i class="bi bi-gear me-2"></i>
                                        Settings
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form action="<?= base_url('dakoii/logout') ?>" method="post" class="d-inline">
                                        <?= csrf_field() ?>
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right me-2"></i>
                                            Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Page Content -->
            <main class="container-fluid p-4">
                <!-- Breadcrumbs -->
                <?php if (!empty($breadcrumbs)): ?>
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('dakoii/dashboard') ?>">
                                <i class="bi bi-house-door"></i> Dashboard
                            </a>
                        </li>
                        <?php foreach ($breadcrumbs as $crumb): ?>
                            <?php if ($crumb['active'] ?? false): ?>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <?= esc($crumb['title']) ?>
                                </li>
                            <?php else: ?>
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url($crumb['url']) ?>">
                                        <?= esc($crumb['title']) ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </nav>
                <?php endif; ?>
                
                <!-- Flash Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?= esc(session()->getFlashdata('success')) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <?= esc(session()->getFlashdata('error')) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('warning')): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <?= esc(session()->getFlashdata('warning')) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('info')): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <?= esc(session()->getFlashdata('info')) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Main Content Area -->
                <?= $content ?>
            </main>
        </div>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay d-md-none" onclick="toggleSidebar()"></div>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= base_url('assets/js/dakoii-app.js') ?>"></script>
    
    <!-- Additional Scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
</body>
</html>
